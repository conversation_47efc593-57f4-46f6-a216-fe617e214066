import { firestore } from '~/helpers/firebase'
import { getAdminUser } from '~/services/db'

export default defineEventHandler(async (event) => {
  const user = await getAdminUser(event.context.auth.uid)
  if (!user) {
    throw createError({ statusCode: 401, message: 'Unauthorized' })
  }

  const workflowId = event.context.params?.id
  if (!workflowId) {
    throw createError({ statusCode: 400, message: 'Workflow ID is required' })
  }

  const body = await readBody(event)

  try {
    // First, verify the workflow belongs to the user
    const workflowSnap = await firestore.doc(`workflows/${workflowId}`).get()
    if (!workflowSnap.exists) {
      throw createError({ statusCode: 404, message: 'Workflow not found' })
    }

    const workflowData = workflowSnap.data()
    if (workflowData?.userId !== user.id) {
      throw createError({ statusCode: 401, message: 'Unauthorized' })
    }

    // Update the workflow
    await firestore.doc(`workflows/${workflowId}`).update({
      ...body,
      updatedAt: new Date().toISOString()
    })

    return { success: true, id: workflowId }
  } catch (e) {
    console.error('unable to update workflow: ', JSON.stringify(e))
    throw createError({ statusCode: 500, message: 'Unable to update workflow' })
  }
})
